const XLSX = require('xlsx');

// Mapeamento dos valores do enum para os tipos
const enumMapping = {
    0: 'SISTEMA_ADMIN',
    1: 'SISTEMA_FINANCEIRO',
    2: 'DESENVOLVEDOR',
    3: 'RE<PERSON>NDA_ADMIN',
    5: 'REVENDA_ASSISTENTE',
    6: 'SUPORTE_STI3',
    7: 'CANAIS_GERENTE',
    8: 'ANALISTA_CONTEUDO'
};

// Tipos de usuário na ordem das colunas
const tiposUsuario = [
    'SISTEMA_ADMIN',
    'SISTEMA_FINANCEIRO', 
    'DESENVOLVEDOR',
    'REVENDA_ADMIN',
    'RE<PERSON><PERSON><PERSON>_ASSISTENTE',
    'SUPORTE_STI3',
    'CANAIS_GERENTE',
    'ANALISTA_CONTEUDO'
];

// Dados das ações extraídos dos arquivos TypeScript
const acoesData = {
    // acesso_menu.ts
    'MENU_ASSINATURA': [0, 1, 7, 6, 3, 5, 2],
    'MENU_FATURAMENTO_EXIBICAO': [0, 1, 7, 6, 3, 2],
    'MENU_FATURAMENTO_CONFERENCIA': [0, 1, 7, 2],
    'MENU_REVENDA': [0, 7, 6, 2],
    'MENU_CADASTRO': [0, 7, 2],
    'MENU_CADASTRO_PRODUTO': [0, 2],
    'MENU_CADASTRO_SERVICO': [0, 2],
    'MENU_CADASTRO_GRADE_SERVICO': [0, 2, 7],
    'MENU_CADASTRO_USUARIO': [0, 7, 2],
    'MENU_ATUALIZACAO': [0, 2],
    'MENU_FISCAL': [0, 2],
    'MENU_FISCAL_REGRA_FISCAL': [0, 2],
    'MENU_FISCAL_NOTA_FISCAL_URL': [0, 2],
    'MENU_FISCAL_NFCE': [0, 2],
    'MENU_FISCAL_NF_REJEICAO': [0, 2],
    'MENU_FISCAL_NF_VALIDACAO': [0, 2],
    'MENU_IMPORTACAO': [0, 2],
    'MENU_LOG': [0, 2],
    'MENU_HANG_FIRE': [0, 2],
    'MENU_TREINAMENTOS': [8, 0, 2],
    'MENU_ARTIGOS_VIDEOS': [8, 0, 2],
    'MENU_TEMAS': [8, 0, 2],
    'MENU_CATEGORIAS_TREINAMENTO': [8, 0, 2],
    'MENU_BANNER': [8, 0, 2],
    
    // assinatura.ts
    'ASSINATURA_EXIBIR_BANCO_DADOS': [0, 2],
    'ENVIO_DE_LOGS': [0, 2],
    'CONTA_CLIENTE_ALTERAR': [0, 7, 2],
    'CONTA_CLIENTE_BLOQUEAR_DESBLOQUEAR': [0, 2],
    'CONTA_CLIENTE_VISUALIZAR_BANCO_DADOS': [0, 2],
    'CONTA_CLIENTE_CADASTRAR_LOJA': [0, 7, 2],
    'CONTA_CLIENTE_ALTERAR_REVENDA': [0, 7],
    'ASSINATURA_CADASTRAR': [0, 7, 2],
    'ASSINATURA_VALIDAR_BLOQUEAR': [0, 1, 7, 3, 2],
    'ASSINATURA_LIBERACAO_PROVISORIA': [0, 1, 7, 6, 3, 5, 2],
    'ASSINATURA_EXIBIR_FATURAMENTO': [0, 1, 7, 6, 3, 5, 2],
    'ASSINATURA_GERAR_FATURAMENTO': [0, 2],
    'ASSINATURA_ALTERAR': [0, 7, 2],
    'ASSINATURA_DIA_VENCIMENTO': [0, 1, 2],
    'ASSINATURA_ALTERAR_SERVICO': [0, 7, 2],
    'ASSINATURA_EXCLUIR_SERVICO': [0, 7, 2],
    'ASSINATURA_CANCELAR_REATIVAR': [0, 7, 2],
    
    // atualizacao.ts
    'CADASTRAR_ATUALIZACOES': [0, 2],
    'ALTERAR_ATUALIZACOES': [0, 2],
    'EXCLUIR_ATUALIZACOES': [0, 2],
    'VISUALIZAR_GERENCIAR_ATUALIZACAO': [0, 2],
    'ATUALIZAR_GERENCIAR_ATUALIZACAO': [0, 2],
    'ATUALIZAR_TODOS_GERENCIAR_ATUALIZACAO': [0, 2],
    
    // cadastro_gradeservico.ts
    'VISUALIZAR_GRADE_SERVICOS': [0, 2],
    'CADASTRAR_GRADE_SERVICOS': [0, 2],
    'ALTERAR_GRADE_SERVICOS': [0, 2],
    'EXCLUIR_GRADE_SERVICOS': [0, 2],
    
    // cadastro_produto.ts
    'VISUALIZAR_PRODUTOS': [0, 2],
    'CADASTRAR_PRODUTOS': [0, 2],
    'ALTERAR_PRODUTOS': [0, 2],
    'EXCLUIR_PRODUTOS': [0, 2],
    
    // cadastro_revenda.ts
    'CADASTRAR_REVENDAS': [0, 7, 2],
    'LOGAR_REVENDAS': [0, 7, 2, 6],
    'ALTERAR_REVENDAS': [0, 7, 3, 2],
    'EXCLUIR_REVENDAS': [0, 7, 2],
    'PARAMETROS_STI3_REVENDAS': [0, 2],
    
    // cadastro_servico.ts
    'VISUALIZAR_SERVICOS': [0, 2],
    'CADASTRAR_SERVICOS': [0, 2],
    'ALTERAR_SERVICOS': [0, 2],
    'EXCLUIR_SERVICOS': [0, 2],
    
    // cadastro_tabelapreco.ts
    'VISUALIZAR_TABELA_PRECOS': [0, 2],
    'ALTERAR_TABELA_PRECOS': [0, 2],
    'CADASTRAR_TABELA_PRECOS': [0, 2],
    'EXCLUIR_TABELA_PRECOS': [0, 2],
    'ALTERAR_INFO_TABELA_PRECOS': [0, 2],
    
    // cadastro_usuario.ts
    'USUARIO_LISTAGEM': [0, 2, 7, 6],
    'USUARIO_CADASTRAR': [0, 7, 2],
    'USUARIO_ALTERAR': [0, 2, 7, 3, 6],
    'USUARIO_EXCLUIR': [0, 7, 2],
    
    // faturamento.ts
    'FATURAMENTO_VER_DETALHES': [0, 1, 7, 6, 3, 5, 2],
    'FATURAMENTO_EXCLUIR': [0, 2],
    'FATURAMENTO_EXIBIR': [0, 1, 7, 6, 3, 5, 2],
    'FATURAMENTO_LANCAR_VALOR': [0, 1, 2],
    'FATURAMENTO_CONFERENCIA': [0, 1, 7, 2],
    
    // fiscal_nfce.ts
    'VISUALIZAR_NFCE': [0, 2],
    'CADASTRAR_NFCE': [0, 2],
    'ALTERAR_NFCE': [0, 2],
    'EXCLUIR_NFCE': [0, 2],
    
    // fiscal_notafiscalrejeicao.ts
    'VISUALIZAR_NF_REJEICAO': [0, 2],
    'CADASTRAR_NF_REJEICAO': [0, 2],
    'ALTERAR_NF_REJEICAO': [0, 2],
    'EXCLUIR_NF_REJEICAO': [0, 2],
    
    // fiscal_notafiscalurl.ts
    'VISUALIZAR_NF_SERVICO': [0, 2],
    'CADASTRAR_NF_SERVICO': [0, 2],
    'ALTERAR_NF_SERVICO': [0, 2],
    'EXCLUIR_NF_SERVICO': [0, 2],
    
    // fiscal_notafiscalvalidacao.ts
    'VISUALIZAR_NF_VALIDACOES': [0, 2],
    'CADASTRAR_NF_VALIDACOES': [0, 2],
    'ALTERAR_NF_VALIDACOES': [0, 2],
    'EXCLUIR_NF_VALIDACOES': [0, 2],
    
    // fiscal_regrafiscalurl.ts
    'VISUALIZAR_REGRAS_FISCAIS': [0, 2],
    'CADASTRAR_REGRAS_FISCAIS': [0, 2],
    'ALTERAR_REGRAS_FISCAIS': [0, 2],
    'EXCLUIR_REGRAS_FISCAIS': [0, 2],
    
    // importacao_ncm.ts
    'VISUALIZAR_IMPORTACAO_NCM': [0, 2],
    'CADASTRAR_IMPORTACAO_NCM': [0, 2],
    
    // log_logerros.ts
    'EXCLUIR_LOG_ERROS': [0, 2],
    'EXCLUIR_TODOS_LOG_ERROS': [0, 2],
    
    // zenflix.ts
    'VISUALIZAR_VIDEOS': [8, 2, 0],
    'CADASTRAR_VIDEOS': [8, 2, 0],
    'ALTERAR_VIDEOS': [8, 2, 0],
    'EXCLUIR_VIDEOS': [8, 2, 0],
    'VISUALIZAR_TEMAS': [8, 2, 0],
    'CADASTRAR_TEMAS': [8, 2, 0],
    'ALTERAR_TEMAS': [8, 2, 0],
    'EXCLUIR_TEMAS': [8, 2, 0],
    'VISUALIZAR_BANNERS': [8, 2, 0],
    'CADASTRAR_BANNERS': [8, 2, 0],
    'ALTERAR_BANNERS': [8, 2, 0],
    'EXCLUIR_BANNERS': [8, 2, 0],
    'VISUALIZAR_CATEGORIAS': [8, 2, 0],
    'CADASTRAR_CATEGORIAS': [8, 2, 0],
    'ALTERAR_CATEGORIAS': [8, 2, 0],
    'EXCLUIR_CATEGORIAS': [8, 2, 0],
    'VISUALIZAR_ARTIGOS': [8, 2, 0],
    'CADASTRAR_ARTIGOS': [8, 2, 0],
    'ALTERAR_ARTIGOS': [8, 2, 0],
    'EXCLUIR_ARTIGOS': [8, 2, 0],
};

function criarPlanilhaExcel() {
    console.log('Iniciando criação da planilha Excel...');
    
    // Criar dados da planilha
    const dados = [];
    
    // Cabeçalho
    const cabecalho = ['Ação', ...tiposUsuario];
    dados.push(cabecalho);
    
    // Ordenar ações alfabeticamente
    const acoesOrdenadas = Object.keys(acoesData).sort();
    
    // Processar cada ação
    acoesOrdenadas.forEach(acao => {
        const usuariosEnum = acoesData[acao];
        const linha = [acao];
        
        // Para cada tipo de usuário, verificar se tem permissão
        tiposUsuario.forEach(tipo => {
            // Encontrar o valor enum correspondente ao tipo
            const enumValue = Object.keys(enumMapping).find(key => enumMapping[key] === tipo);
            const temPermissao = usuariosEnum.includes(parseInt(enumValue));
            linha.push(temPermissao ? 'SIM' : 'NÃO');
        });
        
        dados.push(linha);
    });
    
    // Criar workbook
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(dados);
    
    // Configurar largura das colunas
    const colWidths = [
        { wch: 40 }, // Coluna Ação
        { wch: 15 }, // SISTEMA_ADMIN
        { wch: 18 }, // SISTEMA_FINANCEIRO
        { wch: 15 }, // DESENVOLVEDOR
        { wch: 15 }, // REVENDA_ADMIN
        { wch: 18 }, // REVENDA_ASSISTENTE
        { wch: 15 }, // SUPORTE_STI3
        { wch: 15 }, // CANAIS_GERENTE
        { wch: 18 }  // ANALISTA_CONTEUDO
    ];
    ws['!cols'] = colWidths;
    
    // Adicionar worksheet ao workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Mapeamento de Ações');
    
    // Salvar arquivo
    const nomeArquivo = 'Mapeamento_Acoes_Funcionalidades.xlsx';
    XLSX.writeFile(wb, nomeArquivo);
    
    console.log(`✅ Planilha Excel criada com sucesso: ${nomeArquivo}`);
    console.log(`📊 Total de ações mapeadas: ${acoesOrdenadas.length}`);
    
    // Estatísticas por tipo de usuário
    console.log('\n📈 Estatísticas por tipo de usuário:');
    tiposUsuario.forEach(tipo => {
        const enumValue = Object.keys(enumMapping).find(key => enumMapping[key] === tipo);
        let contador = 0;
        
        Object.values(acoesData).forEach(usuarios => {
            if (usuarios.includes(parseInt(enumValue))) {
                contador++;
            }
        });
        
        console.log(`   ${tipo}: ${contador} permissões`);
    });
    
    return nomeArquivo;
}

// Executar a função
try {
    criarPlanilhaExcel();
} catch (error) {
    console.error('❌ Erro ao criar planilha:', error);
}
