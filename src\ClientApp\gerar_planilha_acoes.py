#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para gerar planilha Excel com mapeamento de ações e permissões por tipo de usuário
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows

# Definir os tipos de usuário conforme o enum
tipos_usuario = [
    'SISTEMA_ADMIN',
    'SISTEMA_FINANCEIRO', 
    'DESENVOLVEDOR',
    'REVENDA_ADMIN',
    'REVENDA_ASSISTENTE',
    'SUPORTE_STI3',
    'CANAIS_GERENTE',
    'ANALISTA_CONTEUDO'
]

# Mapeamento dos valores do enum para os tipos
enum_mapping = {
    0: 'SISTEMA_ADMIN',
    1: 'SISTEMA_FINANCEIRO',
    2: 'DESENVOLVEDOR',
    3: 'REVENDA_ADMIN',
    5: 'REVENDA_ASSISTENTE',
    6: 'SUPORTE_STI3',
    7: 'CANAIS_GERENTE',
    8: 'ANALISTA_CONTEUDO'
}

# Dados das ações extraídos dos arquivos TypeScript
acoes_dados = {
    # acesso_menu.ts
    'MENU_ASSINATURA': [0, 1, 7, 6, 3, 5, 2],
    'MENU_FATURAMENTO_EXIBICAO': [0, 1, 7, 6, 3, 2],
    'MENU_FATURAMENTO_CONFERENCIA': [0, 1, 7, 2],
    'MENU_REVENDA': [0, 7, 6, 2],
    'MENU_CADASTRO': [0, 7, 2],
    'MENU_CADASTRO_PRODUTO': [0, 2],
    'MENU_CADASTRO_SERVICO': [0, 2],
    'MENU_CADASTRO_GRADE_SERVICO': [0, 2, 7],
    'MENU_CADASTRO_USUARIO': [0, 7, 2],
    'MENU_ATUALIZACAO': [0, 2],
    'MENU_FISCAL': [0, 2],
    'MENU_FISCAL_REGRA_FISCAL': [0, 2],
    'MENU_FISCAL_NOTA_FISCAL_URL': [0, 2],
    'MENU_FISCAL_NFCE': [0, 2],
    'MENU_FISCAL_NF_REJEICAO': [0, 2],
    'MENU_FISCAL_NF_VALIDACAO': [0, 2],
    'MENU_IMPORTACAO': [0, 2],
    'MENU_LOG': [0, 2],
    'MENU_HANG_FIRE': [0, 2],
    'MENU_TREINAMENTOS': [8, 0, 2],
    'MENU_ARTIGOS_VIDEOS': [8, 0, 2],
    'MENU_TEMAS': [8, 0, 2],
    'MENU_CATEGORIAS_TREINAMENTO': [8, 0, 2],
    'MENU_BANNER': [8, 0, 2],
    
    # assinatura.ts
    'ASSINATURA_EXIBIR_BANCO_DADOS': [0, 2],
    'ENVIO_DE_LOGS': [0, 2],
    'CONTA_CLIENTE_ALTERAR': [0, 7, 2],
    'CONTA_CLIENTE_BLOQUEAR_DESBLOQUEAR': [0, 2],
    'CONTA_CLIENTE_VISUALIZAR_BANCO_DADOS': [0, 2],
    'CONTA_CLIENTE_CADASTRAR_LOJA': [0, 7, 2],
    'CONTA_CLIENTE_ALTERAR_REVENDA': [0, 7],
    'ASSINATURA_CADASTRAR': [0, 7, 2],
    'ASSINATURA_VALIDAR_BLOQUEAR': [0, 1, 7, 3, 2],
    'ASSINATURA_LIBERACAO_PROVISORIA': [0, 1, 7, 6, 3, 5, 2],
    'ASSINATURA_EXIBIR_FATURAMENTO': [0, 1, 7, 6, 3, 5, 2],
    'ASSINATURA_GERAR_FATURAMENTO': [0, 2],
    'ASSINATURA_ALTERAR': [0, 7, 2],
    'ASSINATURA_DIA_VENCIMENTO': [0, 1, 2],
    'ASSINATURA_ALTERAR_SERVICO': [0, 7, 2],
    'ASSINATURA_EXCLUIR_SERVICO': [0, 7, 2],
    'ASSINATURA_CANCELAR_REATIVAR': [0, 7, 2],
    
    # atualizacao.ts
    'CADASTRAR_ATUALIZACOES': [0, 2],
    'ALTERAR_ATUALIZACOES': [0, 2],
    'EXCLUIR_ATUALIZACOES': [0, 2],
    'VISUALIZAR_GERENCIAR_ATUALIZACAO': [0, 2],
    'ATUALIZAR_GERENCIAR_ATUALIZACAO': [0, 2],
    'ATUALIZAR_TODOS_GERENCIAR_ATUALIZACAO': [0, 2],
    
    # cadastro_gradeservico.ts
    'VISUALIZAR_GRADE_SERVICOS': [0, 2],
    'CADASTRAR_GRADE_SERVICOS': [0, 2],
    'ALTERAR_GRADE_SERVICOS': [0, 2],
    'EXCLUIR_GRADE_SERVICOS': [0, 2],
    
    # cadastro_produto.ts
    'VISUALIZAR_PRODUTOS': [0, 2],
    'CADASTRAR_PRODUTOS': [0, 2],
    'ALTERAR_PRODUTOS': [0, 2],
    'EXCLUIR_PRODUTOS': [0, 2],
    
    # cadastro_revenda.ts
    'CADASTRAR_REVENDAS': [0, 7, 2],
    'LOGAR_REVENDAS': [0, 7, 2, 6],
    'ALTERAR_REVENDAS': [0, 7, 3, 2],
    'EXCLUIR_REVENDAS': [0, 7, 2],
    'PARAMETROS_STI3_REVENDAS': [0, 2],
    
    # cadastro_servico.ts
    'VISUALIZAR_SERVICOS': [0, 2],
    'CADASTRAR_SERVICOS': [0, 2],
    'ALTERAR_SERVICOS': [0, 2],
    'EXCLUIR_SERVICOS': [0, 2],
    
    # cadastro_tabelapreco.ts
    'VISUALIZAR_TABELA_PRECOS': [0, 2],
    'ALTERAR_TABELA_PRECOS': [0, 2],
    'CADASTRAR_TABELA_PRECOS': [0, 2],
    'EXCLUIR_TABELA_PRECOS': [0, 2],
    'ALTERAR_INFO_TABELA_PRECOS': [0, 2],
    
    # cadastro_usuario.ts
    'USUARIO_LISTAGEM': [0, 2, 7, 6],
    'USUARIO_CADASTRAR': [0, 7, 2],
    'USUARIO_ALTERAR': [0, 2, 7, 3, 6],
    'USUARIO_EXCLUIR': [0, 7, 2],
    
    # faturamento.ts
    'FATURAMENTO_VER_DETALHES': [0, 1, 7, 6, 3, 5, 2],
    'FATURAMENTO_EXCLUIR': [0, 2],
    'FATURAMENTO_EXIBIR': [0, 1, 7, 6, 3, 5, 2],
    'FATURAMENTO_LANCAR_VALOR': [0, 1, 2],
    'FATURAMENTO_CONFERENCIA': [0, 1, 7, 2],
    
    # fiscal_nfce.ts
    'VISUALIZAR_NFCE': [0, 2],
    'CADASTRAR_NFCE': [0, 2],
    'ALTERAR_NFCE': [0, 2],
    'EXCLUIR_NFCE': [0, 2],
    
    # fiscal_notafiscalrejeicao.ts
    'VISUALIZAR_NF_REJEICAO': [0, 2],
    'CADASTRAR_NF_REJEICAO': [0, 2],
    'ALTERAR_NF_REJEICAO': [0, 2],
    'EXCLUIR_NF_REJEICAO': [0, 2],
    
    # fiscal_notafiscalurl.ts
    'VISUALIZAR_NF_SERVICO': [0, 2],
    'CADASTRAR_NF_SERVICO': [0, 2],
    'ALTERAR_NF_SERVICO': [0, 2],
    'EXCLUIR_NF_SERVICO': [0, 2],
    
    # fiscal_notafiscalvalidacao.ts
    'VISUALIZAR_NF_VALIDACOES': [0, 2],
    'CADASTRAR_NF_VALIDACOES': [0, 2],
    'ALTERAR_NF_VALIDACOES': [0, 2],
    'EXCLUIR_NF_VALIDACOES': [0, 2],
    
    # fiscal_regrafiscalurl.ts
    'VISUALIZAR_REGRAS_FISCAIS': [0, 2],
    'CADASTRAR_REGRAS_FISCAIS': [0, 2],
    'ALTERAR_REGRAS_FISCAIS': [0, 2],
    'EXCLUIR_REGRAS_FISCAIS': [0, 2],
    
    # importacao_ncm.ts
    'VISUALIZAR_IMPORTACAO_NCM': [0, 2],
    'CADASTRAR_IMPORTACAO_NCM': [0, 2],
    
    # log_logerros.ts
    'EXCLUIR_LOG_ERROS': [0, 2],
    'EXCLUIR_TODOS_LOG_ERROS': [0, 2],
    
    # zenflix.ts
    'VISUALIZAR_VIDEOS': [8, 2, 0],
    'CADASTRAR_VIDEOS': [8, 2, 0],
    'ALTERAR_VIDEOS': [8, 2, 0],
    'EXCLUIR_VIDEOS': [8, 2, 0],
    'VISUALIZAR_TEMAS': [8, 2, 0],
    'CADASTRAR_TEMAS': [8, 2, 0],
    'ALTERAR_TEMAS': [8, 2, 0],
    'EXCLUIR_TEMAS': [8, 2, 0],
    'VISUALIZAR_BANNERS': [8, 2, 0],
    'CADASTRAR_BANNERS': [8, 2, 0],
    'ALTERAR_BANNERS': [8, 2, 0],
    'EXCLUIR_BANNERS': [8, 2, 0],
    'VISUALIZAR_CATEGORIAS': [8, 2, 0],
    'CADASTRAR_CATEGORIAS': [8, 2, 0],
    'ALTERAR_CATEGORIAS': [8, 2, 0],
    'EXCLUIR_CATEGORIAS': [8, 2, 0],
    'VISUALIZAR_ARTIGOS': [8, 2, 0],
    'CADASTRAR_ARTIGOS': [8, 2, 0],
    'ALTERAR_ARTIGOS': [8, 2, 0],
    'EXCLUIR_ARTIGOS': [8, 2, 0],
}

def criar_planilha():
    """Cria a planilha Excel com o mapeamento de ações e permissões"""
    
    # Criar DataFrame
    data = []
    
    for acao, usuarios_enum in acoes_dados.items():
        row = {'Ação': acao}
        
        # Inicializar todas as colunas com "NÃO"
        for tipo in tipos_usuario:
            row[tipo] = 'NÃO'
        
        # Marcar "SIM" para os tipos de usuário que têm acesso
        for enum_value in usuarios_enum:
            if enum_value in enum_mapping:
                tipo_usuario = enum_mapping[enum_value]
                row[tipo_usuario] = 'SIM'
        
        data.append(row)
    
    # Criar DataFrame
    df = pd.DataFrame(data)
    
    # Ordenar por ação
    df = df.sort_values('Ação')
    
    # Criar workbook do Excel
    wb = Workbook()
    ws = wb.active
    ws.title = "Mapeamento de Ações"
    
    # Adicionar dados
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)
    
    # Formatação
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    center_alignment = Alignment(horizontal="center", vertical="center")
    
    # Formatação do cabeçalho
    for cell in ws[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
    
    # Formatação das células de dados
    sim_fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
    nao_fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")
    
    for row in ws.iter_rows(min_row=2):
        for cell in row:
            cell.alignment = center_alignment
            if cell.value == "SIM":
                cell.fill = sim_fill
            elif cell.value == "NÃO":
                cell.fill = nao_fill
    
    # Ajustar largura das colunas
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Salvar arquivo
    filename = "Mapeamento_Acoes_Funcionalidades.xlsx"
    wb.save(filename)
    print(f"Planilha criada com sucesso: {filename}")
    print(f"Total de ações mapeadas: {len(acoes_dados)}")
    
    return filename

if __name__ == "__main__":
    criar_planilha()
