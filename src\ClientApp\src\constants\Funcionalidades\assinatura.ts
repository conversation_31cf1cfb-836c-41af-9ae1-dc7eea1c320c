import { EnumTipoUsu<PERSON> } from 'constants/Enum/enumTipoUsuario';

export const AssinaturaAcao = {
  //#region LISTAGEM DE ASSINATURA
  ASSINATURA_EXIBIR_BANCO_DADOS: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
  //#endregion

  //#region ENVIO DE LOGS
  ENVIO_DE_LOGS: [EnumTipoUsuario.SISTEMA_ADMIN, EnumTipoUsuario.DESENVOLVEDOR],
  //#endregion

  //#region MENU DE AÇÕES - CONTA CLIENTE
  CONTA_CLIENTE_ALTERAR: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  CONTA_CLIENTE_BLOQUEAR_DESBLOQUEAR: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  CONTA_CLIENTE_VISUALIZAR_BANCO_DADOS: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  CONTA_CLIENTE_CADASTRAR_LOJA: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  CONTA_CLIENTE_ALTERAR_REVENDA: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
  ],
  //#endregion

  //#region MENU DE AÇÕES - ASSINATURA
  ASSINATURA_CADASTRAR: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

    ASSINATURA_VALIDAR_BLOQUEAR: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.SISTEMA_FINANCEIRO,
    EnumTipoUsuario.CANAIS_GERENTE,
    
    EnumTipoUsuario.REVENDA_ADMIN,

    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  ASSINATURA_LIBERACAO_PROVISORIA: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.SISTEMA_FINANCEIRO,

    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.SUPORTE_STI3,

    EnumTipoUsuario.REVENDA_ADMIN,

    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  ASSINATURA_EXIBIR_FATURAMENTO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.SISTEMA_FINANCEIRO,

    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.SUPORTE_STI3,

    EnumTipoUsuario.REVENDA_ADMIN,

    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  ASSINATURA_GERAR_FATURAMENTO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  ASSINATURA_ALTERAR: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

    ASSINATURA_DIA_VENCIMENTO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.SISTEMA_FINANCEIRO,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  ASSINATURA_ALTERAR_SERVICO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  ASSINATURA_EXCLUIR_SERVICO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  ASSINATURA_CANCELAR_REATIVAR: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
};
