import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FiEdit, FiAlertTriangle, FiSearch, FiUnlock } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Box, Flex, Td, Text, Tr } from '@chakra-ui/react';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { EnumStatus } from 'constants/Enum/enumStatus';
import { getClienteObterResponsavel } from 'helpers/functions/getClienteObterResponsavel';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import api, { ResponseApi } from 'services/api';
import {
  ResponsavelProps,
  useAssinaturasContext,
} from 'store/Assinaturas/AssinaturaContext';
import auth from 'modules/auth';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { formattingCodeInLabel } from 'helpers/format/formattingCodeInLabel';
import { formatDate, formatDateMesAno } from 'helpers/format/formatStringDate';
import { clienteAlterarRevenda } from 'api/Cliente/AlterarRevenda';

import { ActionsMenu } from 'components/ActionsMenu';
import { ButtonDefault } from 'components/Button';
import { ContainerListagem } from 'components/Grid/GridList';
import { Pagination } from 'components/Grid/Pagination';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { ModalCancelar } from 'components/Modal/ModalCancelar';
import { ModalWarning } from 'components/Modal/ModalWarning';
import { SelectDefault } from 'components/Select/SelectDefault';
import { IconTooltip } from 'components/IconTooltip';

import {
  AssinaturaProps,
  formDefaultValues,
  FormData,
} from './validationForms';

import { ModalClienteAlterarRevenda } from '../components/ModalClienteAlterarRevenda';
import React from 'react';

export const ListarAssinatura = () => {
  const {
    setContaCliente,
    setListDataResponsavel,
    setIsUpdateFormDominio,
    setListServicosAdicionais,
    setCurrentValuesFiltrosAssinatura,
    currentValuesFiltrosAssinatura,
  } = useAssinaturasContext();

  const formDefault =
    currentValuesFiltrosAssinatura !== undefined
      ? currentValuesFiltrosAssinatura
      : formDefaultValues;

  const [listarAssinatura, setListarAssinatura] = useState<AssinaturaProps[]>(
    []
  );
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [currentFilters, setCurrentFilters] = useState<FormData>(formDefault);

  const formMethods = useForm<FormData>({
    defaultValues: formDefault,
  });

  const navigate = useNavigate();

  const { setValue } = formMethods;

  const acessoContaClienteAcoes = auth.usuarioPossuiPermissao(
    Array.from(
      new Set(
        [
          ConstantFuncionalidades.AssinaturaAcao.CONTA_CLIENTE_ALTERAR,
          ConstantFuncionalidades.AssinaturaAcao
            .CONTA_CLIENTE_BLOQUEAR_DESBLOQUEAR,
          ConstantFuncionalidades.AssinaturaAcao.CONTA_CLIENTE_CADASTRAR_LOJA,
        ].flatMap((item) => item)
      )
    )
  );

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<AssinaturaProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_CLIENTES_ASSINATURA,
          gridPaginadaConsulta
        ),
        {
          params: currentFilters,
        }
      );

      if (response.sucesso) {
        const { total, registros } = response.dados;
        setCurrentValuesFiltrosAssinatura(currentFilters);
        setTotalRegistros(total);
        setListarAssinatura(registros);
      }

      setIsLoading(false);
    },
    [currentFilters, recarregarListagem]
  );

  const alterarStatusCliente = useCallback(
    async (id: string, status: number, motivo?: string) => {
      const response = await api.post<void, ResponseApi>(
        `${
          ConstantEnderecoWebservice.BLOQUEAR_CLIENTE_ASSINATURA
        }?${new URLSearchParams(Object({ id, status, motivo }))}`
      );

      return response;
    },
    []
  );

  const checarAcoesAssinatura = useCallback(
    (assinaturaStatus: number, assinaturaId: string) => {
      let acoes = [
        {
          content: 'Desbloquear',
          onClick: () =>
            handleBloquearAssinatura(assinaturaId, EnumStatus.ATIVO.value),
          possuiFuncionalidade: auth.usuarioPossuiPermissao(
            ConstantFuncionalidades.AssinaturaAcao
              .CONTA_CLIENTE_BLOQUEAR_DESBLOQUEAR
          ),
        },
      ];

      if (EnumStatus.ATIVO.value === assinaturaStatus) {
        return (acoes = [
          {
            content: 'Editar',
            onClick: async () => handleAlterarClientes(assinaturaId, true),
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AssinaturaAcao.CONTA_CLIENTE_ALTERAR
            ),
          },

          {
            content: 'Bloquear',
            onClick: () =>
              handleBloquearAssinatura(
                assinaturaId,
                EnumStatus.BLOQUEADO.value
              ),
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AssinaturaAcao
                .CONTA_CLIENTE_BLOQUEAR_DESBLOQUEAR
            ),
          },
          {
            content: 'Criar nova loja',
            onClick: () => handleCriarNovaLoja(assinaturaId),
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AssinaturaAcao
                .CONTA_CLIENTE_CADASTRAR_LOJA
            ),
          },
          {
            content: 'Alterar revenda do cliente',
            onClick: async () =>
              ModalClienteAlterarRevenda({
                aoConfirmar: async ({ onClose, data }) => {
                  const response = await clienteAlterarRevenda({
                    clienteId: assinaturaId,
                    revendaId: data.revenda?.value ?? '',
                  });

                  if (response?.avisos) {
                    response.avisos.forEach((aviso) => toast.warning(aviso));
                  }

                  if (response?.sucesso) {
                    onClose();
                  }
                },
                aoCancelar: ({ onClose }) => onClose(),
              }),
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AssinaturaAcao
                .CONTA_CLIENTE_ALTERAR_REVENDA
            ),
          },
        ]);
      }
      if (EnumStatus.BLOQUEADO.value === assinaturaStatus) {
        return (acoes = [
          ...acoes,
          {
            content: 'Excluir',
            onClick: () =>
              handleExcluirAssinatura(assinaturaId, EnumStatus.EXCLUIR.value),
            possuiFuncionalidade: true,
          },
        ]);
      }
      return acoes;
    },
    [recarregarListagem]
  );

  const handleBloquearAssinatura = useCallback(
    async (id: string, status: number) => {
      const isAtivo = EnumStatus.ATIVO.value === status;

      if (!isAtivo) {
        ModalCancelar({
          title: 'Você tem certeza?',
          description: 'Antes de confirmar, informe o motivo do cancelamento.',
          confirmButtonText: 'Sim, continuar!',
          cancelButtonText: 'Cancelar',
          onConfirm: async (motivo) => {
            const response = await alterarStatusCliente(id, status, motivo);
            if (response.sucesso) {
              toast.success(`A assinatura foi bloqueada com sucesso.`);
              setRecarregarListagem(!recarregarListagem);
              return true;
            }
            return false;
          },
        });
      } else {
        setIsLoading(true);
        const response = await alterarStatusCliente(id, status);
        if (response.sucesso) {
          toast.success('A assinatura foi desbloqueada com sucesso.');
          setRecarregarListagem(!recarregarListagem);
        }
        setIsLoading(false);
      }
    },
    [recarregarListagem, alterarStatusCliente]
  );

  const handleExcluirAssinatura = useCallback(
    async (id: string, status: number) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description:
          'O banco de dados desse cliente será excluido em algumas horas, você tem certeza que quer excluir?',
        confirmButtonText: 'Sim, continuar!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await alterarStatusCliente(id, status);
          if (response.sucesso) {
            toast.success(
              `A solicitação da exclusão do banco de dados foi realizada com sucesso.`
            );
            setRecarregarListagem(!recarregarListagem);
            return true;
          }
          return false;
        },
      });
    },
    [recarregarListagem, alterarStatusCliente]
  );

  const handleCriarNovaLoja = useCallback(
    async (idCliente: string) => {
      setIsLoading(true);
      setContaCliente(idCliente);

      const response = await getClienteObterResponsavel(idCliente);

      if (response.sucesso) {
        setListDataResponsavel(response.dados);
        navigate(ConstanteRotas.ASSINATURAS_CADASTRAR);
      }
      setIsLoading(false);
    },
    [setContaCliente, navigate, getClienteObterResponsavel]
  );

  const handleLiberarTemporiariamenteAssinatura = useCallback(
    async (id: string) => {
      const dataAtual = new Date();
      ModalWarning({
        title: 'Você tem certeza?',
        description: `Confirma a liberação provisóriaaté ${formatDate(
          new Date(dataAtual.setDate(dataAtual.getDate() + 5))
        )}`,
        confirmButtonText: 'Sim, confirmar!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.put<void, ResponseApi>(
            `${ConstantEnderecoWebservice.ASSINATURA_LIBERACAO_PROVISORIA}?assinaturaID=${id}&dias=5`
          );
          if (response.sucesso) {
            toast.success('A assinatura foi liberada com sucesso');
            setRecarregarListagem(!recarregarListagem);
            return true;
          }
          return true;
        },
      });
    },
    [recarregarListagem]
  );

  const handleBloquearAssinaturaData = useCallback(
    async (id: string) => {
      // Obter a assinatura para acessar o diaVencimento
      const assinatura = listarAssinatura.find(a => 
        a.lojas.some(loja => loja.assinaturaId === id)
      );
      
      // Encontrar a loja específica
      const loja = assinatura?.lojas.find(l => l.assinaturaId === id);
      
      if (!loja) {
        toast.error('Não foi possível encontrar a assinatura');
        return;
      }
      
      // Verificar se está bloqueada (data de expiração menor que hoje)
      const hoje = new Date();
      hoje.setHours(0, 0, 0, 0);
      
      let dataExpiracaoObj = null;
      try {
        dataExpiracaoObj = loja.dataExpiracao ? new Date(loja.dataExpiracao) : null;
        if (dataExpiracaoObj && isNaN(dataExpiracaoObj.getTime())) {
          dataExpiracaoObj = null;
        }
      } catch (error) {
        console.error('Erro ao converter data de expiração:', error);
        dataExpiracaoObj = null;
      }
      
      const estaBloqueada = dataExpiracaoObj && dataExpiracaoObj < hoje;
      
      // Calcular a data para o próximo mês com base no diaVencimento
      const dataAtual = new Date();   
      dataAtual.setHours(0, 0, 0, 0);
      // Criar data para o próximo mês
      let dataNova = estaBloqueada       
      ? new Date(dataAtual.getFullYear(), dataAtual.getMonth() + 1, loja.diaVencimento + 10)
      : new Date(dataAtual.getFullYear(), dataAtual.getMonth() - 1, loja.diaVencimento);
      
      const titulo = estaBloqueada ? 'Validar Assinatura' : 'Bloquear Assinatura';
      const descricao = estaBloqueada 
        ? `Confirma a validação da assinatura até ${formatDate(dataNova)}?`
        : `Confirma o bloqueio imediato da assinatura?`;
      
      ModalWarning({
        title: titulo,
        description: descricao,
        confirmButtonText: estaBloqueada ? 'Sim, validar!' : 'Sim, bloquear!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          try {
            const response = await api.put<void, ResponseApi>(
              `${ConstantEnderecoWebservice.ASSINATURA_VALIDAR_BLOQUEAR}?assinaturaID=${id}&data=${dataNova.toISOString()}`
            );
            if (response.sucesso) {
              toast.success(estaBloqueada 
                ? 'A assinatura foi validada com sucesso' 
                : 'A assinatura foi bloqueada com sucesso'
              );
              setRecarregarListagem(!recarregarListagem);
              return true;
            }
            return true;
          } catch (error) {
            console.error('Erro ao validar/bloquear assinatura:', error);
            toast.error('Ocorreu um erro ao processar a solicitação');
            return false;
          }
        },
      });
    },
    [recarregarListagem, listarAssinatura]
  );

  const alternarEnvioDeLogs = async (hostURL?: string) => {
    if (!hostURL) {
      return;
    }

    setIsLoading(true);
    const requestConfig = {
      headers: {
        HTTP_REFERER_MULTIEMPRESA: hostURL,
      },
      baseURL: import.meta.env.VITE_ZENDAR_API_URL,
    };

    const response = await api.patch<void, ResponseApi>(
      ConstantEnderecoWebservice.ALTERAR_ENVIO_DE_LOG,
      null,
      requestConfig
    );
    setIsLoading(false);

    if (response.sucesso) {
      toast.success(response.dados as string);
    }
  };

  const checarAcoesAdicionais = useCallback(
    (assinaturaStatus: number, assinaturaId: string, fantasia: string, hostURL?: string, dataExpiracao?: Date) => {
      // Verifica se a assinatura está bloqueada (data de expiração menor que hoje)
      const hoje = new Date();
      hoje.setHours(0, 0, 0, 0);
      
      const dataExpiracaoObj = dataExpiracao ? new Date(dataExpiracao) : null;
      const estaBloqueada = dataExpiracaoObj && dataExpiracaoObj < hoje;
      
      let acoes = [        
        {
          content: 'Faturas',
          onClick: () => handleExibirFaturamento(assinaturaId, fantasia),
          possuiFuncionalidade: auth.usuarioPossuiPermissao(ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_EXIBIR_FATURAMENTO),
        },
        {
          content: estaBloqueada ? 'Validar' : 'Bloquear',
          onClick: () => handleBloquearAssinaturaData(assinaturaId),
          possuiFuncionalidade: EnumStatus.ATIVO.value === assinaturaStatus &&
          auth.usuarioPossuiPermissao(ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_LIBERACAO_PROVISORIA),
        },
        {
          content: 'Alternar envio de logs',
          onClick: () => alternarEnvioDeLogs(hostURL),
          possuiFuncionalidade: auth.usuarioPossuiPermissao(
            ConstantFuncionalidades.AssinaturaAcao.ENVIO_DE_LOGS
          ),
        },
      ];
      return acoes;
    },
    [recarregarListagem, handleBloquearAssinaturaData]
  );


  const handleAlterarClientes = (operacaoId: string, isDominio: boolean) => {
    setIsUpdateFormDominio(isDominio);
    navigate(
      SubstituirParametroRota(
        ConstanteRotas.ASSINATURAS_ALTERAR,
        'id',
        operacaoId
      )
    );
  };

  const handleExibirFaturamento = useCallback(
    async (assinaturaId: string, fantasia: string) => {
      navigate(ConstanteRotas.ASSINATURAS_FATURAMENTO, {
        state: {
          assinaturaId,
          fantasia,
        },
      });
    },
    []
  );

  useEffect(() => {
    setListDataResponsavel({} as ResponsavelProps);
    setListServicosAdicionais([]);
  }, [setListDataResponsavel, setListServicosAdicionais]);

  useEffect(() => {
    if (currentValuesFiltrosAssinatura !== undefined) {
      setValue(
        'dominio',
        currentValuesFiltrosAssinatura.dominioRazaoSocialFantasiaCnpj
      );
    }
  }, []);

  return (
    <Box>
      <FormProvider {...formMethods}>
        <ContainerListagem
          filtrosListagem={
            <SelectDefault
              asControlledByObject={false}
              name="status"
              placeholder="Selecione um filtro"
              filtrosAtivos
              onSelect={(optionSelecionada) => {
                setCurrentFilters((filtrosJaAdicionados) => ({
                  ...filtrosJaAdicionados,
                  status: optionSelecionada.value,
                }));
              }}
              options={EnumStatus.properties.map((status) => status)}
            />
          }
          inputPesquisa={
            <InputDefault
              maxLength={100}
              placeholder="Buscar assinatura por domínio, razão social, fantasia e cnpj"
              iconLeftElement={FiSearch}
              name="dominio"
              onEnterKeyPress={(value) => {
                setCurrentFilters((filtrosJaAdicionados) => ({
                  ...filtrosJaAdicionados,
                  dominioRazaoSocialFantasiaCnpj: value || '',
                }));
              }}
            />
          }
          buttonCadastrar={
            <ButtonDefault
              onClick={() => navigate(ConstanteRotas.ASSINATURAS_CADASTRAR)}
              width={['full', 'full', 'full', '220px']}
              color="white"
              colorScheme="secondary"
              possuiFuncionalidade={auth.usuarioPossuiPermissao(
                ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_CADASTRAR
              )}
            >
              Cadastrar assinatura
            </ButtonDefault>
          }
        >
          <Pagination
            w={['800px', '800px', '800px', 'full']}
            variant="simple-card"
            sx={{
              '& tr > th': { borderBottom: 'none' },
              '& td:only-child': {
                bg: listarAssinatura.length === 0 ? 'white' : 'gray.50',
                padding: '0',
                border: 'none',
                boxShadow: 'primary',
              },
              '& tr': { boxShadow: 'none' },
              '& th': {
                fontSize: '2xs',
                color: 'gray.300',
                fontWeight: 'bold',
                paddingBottom: '5px',
              },
            }}
            loadColumnsData={paginationHandle}
            nPages={totalRegistros}
            isBorderWidth={false}
            currentPage={page}
            setCurrentPage={setPage}
            isLoading={isLoading}
            defaultOrderDirection="asc"
            defaultKeyOrdered="dominio"
            tableHeaders={[
              {
                key: 'dominio',
                content: (
                  <Flex w="100%" justifyContent="space-between">
                    <Flex>
                      <Text mr="100%">Status</Text>
                      Domínio
                    </Flex>
                    {!acessoContaClienteAcoes && <Text>Ações</Text>}
                  </Flex>
                ),
                isOrderable: false,
              },
            ]}
            renderTableRows={listarAssinatura.map((assinatura, index) => (
              <React.Fragment key={assinatura.id}>
                {index !== 0 && (
                  <Tr>
                    <Td colSpan={4} p="0" h="2px" />
                  </Tr>
                )}
                <Tr key={assinatura.id}>
                  <Td colSpan={4}>
                    <Box>
                      <Flex
                        paddingLeft="25px"
                        pt="4px"
                        pb="4px"
                        pr="15px"
                        borderTopRadius="4px"
                        bg="gray.100"
                        w="full"
                        justifyContent="space-between"
                      >
                        <Flex w="full">
                          <Text w="107px">
                            {formattingCodeInLabel(
                              EnumStatus,
                              assinatura.status
                            )}
                          </Text>
                          {assinatura.dominio}
                        </Flex>
                        {assinatura.status !== EnumStatus.EXCLUIDO.value &&
                          acessoContaClienteAcoes && (
                            <Flex mb="5px" mr="5px">
                              <ActionsMenu
                                id="mostrarMais"
                                items={checarAcoesAssinatura(
                                  assinatura.status,
                                  assinatura.id
                                )}
                              />
                            </Flex>
                          )}
                      </Flex>
                      {assinatura.lojas.map((loja, lojaIndex) => {

                        let colorSituacao = '';
                        let expirando = false;

                        const hoje = new Date();
                        hoje.setHours(0, 0, 0, 0); // Zera horas, minutos, segundos e milissegundos

                        // Garante que loja.dataExpiracao seja um objeto Date válido
                        const dataExpiracao = loja.dataExpiracao instanceof Date 
                          ? loja.dataExpiracao 
                          : new Date(loja.dataExpiracao);

                        // Zera horas, minutos, segundos e milissegundos de dataExpiracao
                        const dataExpiracaoNormalizada = new Date(dataExpiracao);
                        dataExpiracaoNormalizada.setHours(0, 0, 0, 0);

                        if (isNaN(dataExpiracaoNormalizada.getTime())) {
                          // Caso a conversão falhe (data inválida), define uma cor padrão ou trata o erro
                          colorSituacao = 'black.300'; // Cor para indicar erro
                        } else {
                          const diffDias = Math.ceil(
                            (dataExpiracaoNormalizada.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24)
                          ); // Diferença em dias

                          if (diffDias > 10) {
                            colorSituacao = 'green.700'; // Mais de 10 dias, ainda com bastante tempo
                          } else if (diffDias >= 0 && diffDias <= 10) {
                            colorSituacao = 'yellow.700'; // 10 dias ou menos, alerta de expiração próxima
                            expirando = true;
                          } else {
                            colorSituacao = 'red.300'; // No dia ou após a expiração, sistema sem acesso
                            expirando = true;
                          }
                        }

                        return (
                          <Flex
                            key={`${assinatura.id}-${loja.cnpj}-${lojaIndex}`}
                            fontSize="xs"
                            pl="15px"
                            pr="15px"
                            pt="5px"
                            pb="5px"
                            w="full"
                          >
                            <Text minW="130">{loja.cnpj}</Text>
                            <Text minW="28%">{loja.razaoSocial}</Text>
                            <Flex minW="20%" fontSize="inherit" fontWeight="inherit">{loja.fantasia}</Flex>
                            <Flex minW="10%" fontSize="inherit" fontWeight="inherit">{loja.plano}</Flex>
                           {loja.dataCancelamento ? (
                              <Flex minW="10%" fontSize="inherit" fontWeight="bold" color="red.300">
                                {'Cancelado em ' + formatDate(loja.dataCancelamento) + ' Liberado até final do mês!'}
                              </Flex>
                            ) : (
                              <>
                                <Flex color={colorSituacao} fontWeight="bold" minW="10%" fontSize="inherit">
                                  {'Validado até '}
                                  {formatDate(loja.dataExpiracao || new Date())}
                                </Flex>
                                {loja.validacaoProvisoria && (
                                  <Flex
                                    cursor="pointer"
                                    pr="12px"
                                    gap="6px"
                                    mb="5px"
                                    w="0.5%"
                                    justifyContent="flex-end"
                                  >
                                    <IconTooltip
                                      icon={FiAlertTriangle}
                                      placement="left"
                                      color="red.300"
                                      mt="4px"
                                      labelTooltip="Validado Provisoriamente"
                                    />
                                  </Flex>
                                )}
                              </>
                            )}

                              <Flex w="full"/>

                              {assinatura.status === EnumStatus.ATIVO.value && expirando &&
                                (!loja.validacaoProvisoria ||
                                auth.usuarioPossuiPermissao(ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_LIBERACAO_PROVISORIA)
                                ) && (
                            <Flex
                              cursor="pointer"
                              pr="12px"
                              gap="6px"
                              mb="5px"
                              w="1.5%"
                              justifyContent="flex-end"
                            >
                              <IconTooltip
                                icon={FiUnlock}
                                onClick={() =>
                                  handleLiberarTemporiariamenteAssinatura(
                                    loja.assinaturaId
                                  )
                                }
                                placement="left"
                                mt="4px"
                                labelTooltip="Liberar 5 dias"
                              />
                              </Flex>
                            )}
                            
                              {assinatura.status !==
                                EnumStatus.EXCLUIDO.value &&
                                auth.usuarioPossuiPermissao(
                                  ConstantFuncionalidades.AssinaturaAcao
                                    .ASSINATURA_ALTERAR
                                ) && (
                                  <Flex
                              cursor="pointer"
                              pr="12px"
                              gap="6px"
                              mb="5px"
                              w="1.5%"
                              justifyContent="flex-end"
                            >
                                  <IconTooltip
                                    onClick={() =>
                                      handleAlterarClientes(
                                        loja.assinaturaId,
                                        false
                                      )
                                    }
                                    icon={FiEdit}
                                    mt="4px"
                                    placement="left"
                                    labelTooltip="Alterar assinatura"
                                  />
                                  </Flex>
                                )}
                              {(
                                <Flex mb="5px" mr="5px">
                                  <ActionsMenu
                                    id="mostrarMais"
                                    items={checarAcoesAdicionais(
                                      assinatura.status,
                                      loja.assinaturaId,
                                      loja.fantasia,
                                      assinatura.hostURL,
                                      loja.dataExpiracao
                                    )}
                                  />
                                </Flex>
                              )}
                          </Flex>
                        );
                      })}
                    </Box>
                  </Td>
                </Tr>
                {index + 1 === listarAssinatura.length && (
                  <Tr>
                    <Td colSpan={4} p="0" h="2px" />
                  </Tr>
                )}
              </React.Fragment>
            ))}
          />
        </ContainerListagem>
      </FormProvider>
    </Box>
  );
};
